#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from knowledge_base import OllamaLLM

def test_ollama_llm():
    """Test the fixed OllamaLLM class"""
    print("🧪 Testing Fixed OllamaLLM API")
    print("=" * 50)
    
    # Test with llama3:latest (which we know exists)
    llm = OllamaLLM(model_name="llama3:latest")
    
    print(f"Model: {llm.model_name}")
    print(f"Base URL: {llm.base_url}")
    print()
    
    # Test simple generation
    print("🔍 Testing simple generation...")
    test_prompt = "What is 2+2? Answer briefly in one sentence."
    
    try:
        response = llm.generate(test_prompt, max_tokens=50)
        print(f"✅ Success!")
        print(f"Prompt: {test_prompt}")
        print(f"Response: {response}")
        print()
        
        # Test longer generation
        print("🔍 Testing longer generation...")
        longer_prompt = "Explain what artificial intelligence is in simple terms."
        response2 = llm.generate(longer_prompt, max_tokens=200)
        print(f"✅ Success!")
        print(f"Prompt: {longer_prompt}")
        print(f"Response: {response2}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ollama_llm()
