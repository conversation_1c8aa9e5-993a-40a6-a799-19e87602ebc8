#!/usr/bin/env python3
"""
Standalone Ollama API test script
"""

import requests
import json
import time

def test_ollama_endpoints():
    """Test various Ollama endpoints to find the working one"""
    
    base_url = "http://localhost:11434"
    
    # Test basic connectivity
    print("🔍 Testing Ollama connectivity...")
    
    basic_endpoints = [
        "/",
        "/api/version",
        "/api/tags",
        "/api/ps"
    ]
    
    working_endpoints = []
    
    for endpoint in basic_endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"Testing {url}...")
            
            response = requests.get(url, timeout=5)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                working_endpoints.append(endpoint)
                print(f"  ✅ {endpoint} works")
                
                # Show response for important endpoints
                if endpoint in ["/api/tags", "/api/version"]:
                    try:
                        data = response.json()
                        print(f"  Response: {json.dumps(data, indent=2)}")
                    except:
                        print(f"  Response: {response.text[:200]}...")
            else:
                print(f"  ❌ {endpoint} failed")
                
        except Exception as e:
            print(f"  ❌ {endpoint} error: {e}")
    
    print(f"\n✅ Working endpoints: {working_endpoints}")
    
    # Test model availability
    print("\n🦙 Testing model availability...")
    
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"Available models ({len(models)}):")
            for model in models:
                name = model.get('name', 'unknown')
                size = model.get('size', 0)
                print(f"  - {name} ({size // (1024*1024*1024) if size > 0 else 'unknown'} GB)")
            
            # Check for Llama models
            llama_models = [m for m in models if 'llama' in m.get('name', '').lower()]
            if llama_models:
                print(f"\n✅ Found {len(llama_models)} Llama model(s)")
                model_to_test = llama_models[0]['name']
            else:
                print("\n❌ No Llama models found")
                print("Run: ollama pull llama3:8b")
                return
        else:
            print(f"❌ Cannot get model list: {response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return
    
    # Test generation endpoints
    print(f"\n🧠 Testing generation with model: {model_to_test}")
    
    test_prompt = "What is 2+2? Answer briefly."
    
    # Test /api/generate
    print("\nTesting /api/generate...")
    try:
        url = f"{base_url}/api/generate"
        data = {
            "model": model_to_test,
            "prompt": test_prompt,
            "stream": False,
            "options": {"num_predict": 50}
        }
        
        print(f"POST {url}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ /api/generate works!")
            print(f"Response: {result.get('response', 'No response field')}")
        else:
            print(f"❌ /api/generate failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ /api/generate error: {e}")
    
    # Test /api/chat
    print("\nTesting /api/chat...")
    try:
        url = f"{base_url}/api/chat"
        data = {
            "model": model_to_test,
            "messages": [{"role": "user", "content": test_prompt}],
            "stream": False,
            "options": {"num_predict": 50}
        }
        
        print(f"POST {url}")
        response = requests.post(url, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ /api/chat works!")
            message = result.get('message', {})
            print(f"Response: {message.get('content', 'No content field')}")
        else:
            print(f"❌ /api/chat failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ /api/chat error: {e}")
    
    # Test OpenAI-compatible endpoint
    print("\nTesting /v1/completions...")
    try:
        url = f"{base_url}/v1/completions"
        data = {
            "model": model_to_test,
            "prompt": test_prompt,
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        print(f"POST {url}")
        response = requests.post(url, json=data, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ /v1/completions works!")
            choices = result.get('choices', [])
            if choices:
                print(f"Response: {choices[0].get('text', 'No text field')}")
        else:
            print(f"❌ /v1/completions failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ /v1/completions error: {e}")

def test_simple_generation():
    """Test simple generation using curl-like approach"""
    print("\n🎯 Testing simple generation...")
    
    # This mimics: curl http://localhost:11434/api/generate -d '{"model":"llama3:8b","prompt":"Hello"}'
    
    try:
        url = "http://localhost:11434/api/generate"
        payload = {
            "model": "llama3:8b",
            "prompt": "Hello, how are you?",
            "stream": False
        }
        
        print(f"Sending request to {url}")
        print(f"Payload: {json.dumps(payload)}")
        
        response = requests.post(
            url, 
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ Success!")
                print(f"Response: {result}")
            except:
                print(f"Response text: {response.text}")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("🧪 Ollama API Test")
    print("=" * 50)
    
    test_ollama_endpoints()
    test_simple_generation()
    
    print("\n" + "=" * 50)
    print("💡 Tips:")
    print("1. Make sure Ollama is running: ollama serve")
    print("2. Make sure you have a model: ollama pull llama3:8b")
    print("3. Try different model names if llama3:8b doesn't work")
    print("4. Check Ollama version: ollama --version")