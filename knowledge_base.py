# local_knowledge_base.py
import os
import sys
import streamlit as st
import traceback
from typing import List, Dict, Any
import tempfile
import requests
import json

# Try importing optional dependencies with fallbacks
try:
    import fitz  # PyMuPDF
    HAS_PYMUPDF = True
except ImportError:
    HAS_PYMUPDF = False
    st.error("PyMuPDF not installed. Run: pip install PyMuPDF")

try:
    import pytesseract
    from PIL import Image
    HAS_OCR = True
except ImportError:
    HAS_OCR = False
    st.error("OCR dependencies not installed. Run: pip install pytesseract pillow")

try:
    from sentence_transformers import SentenceTransformer
    HAS_EMBEDDINGS = True
except ImportError:
    HAS_EMBEDDINGS = False
    st.error("sentence-transformers not installed. Run: pip install sentence-transformers")

try:
    import chromadb
    from chromadb.config import Settings
    HAS_CHROMADB = True
except ImportError:
    HAS_CHROMADB = False
    st.error("ChromaDB not installed. Run: pip install chromadb")

import io

class OllamaLLM:
    def __init__(self, model_name="llama3:latest", base_url="http://127.0.0.1:11434"):
        self.model_name = model_name
        self.base_url = base_url
    
    def generate(self, prompt: str, max_tokens: int = 512) -> str:
        # Try different API endpoints in order of preference
        # Based on testing, /v1/completions works best, then /api/generate, then /api/chat
        endpoints = [
            "/v1/completions", # OpenAI-compatible endpoint (works best)
            "/api/generate",   # Standard endpoint (may timeout)
            "/api/chat"        # Chat endpoint (may timeout)
        ]

        last_error = None

        for endpoint in endpoints:
            try:
                if endpoint == "/v1/completions":
                    # OpenAI-compatible endpoint
                    url = f"{self.base_url}{endpoint}"
                    data = {
                        "model": self.model_name,
                        "prompt": prompt,
                        "max_tokens": max_tokens,
                        "temperature": 0.7,
                        "stream": False
                    }
                    timeout = 60  # Shorter timeout for this endpoint

                elif endpoint == "/api/generate":
                    # Standard generate endpoint
                    url = f"{self.base_url}{endpoint}"
                    data = {
                        "model": self.model_name,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "num_predict": max_tokens,
                            "temperature": 0.7
                        }
                    }
                    timeout = 180  # Longer timeout for this endpoint

                elif endpoint == "/api/chat":
                    # Chat endpoint
                    url = f"{self.base_url}{endpoint}"
                    data = {
                        "model": self.model_name,
                        "messages": [{"role": "user", "content": prompt}],
                        "stream": False,
                        "options": {
                            "num_predict": max_tokens,
                            "temperature": 0.7
                        }
                    }
                    timeout = 180  # Longer timeout for this endpoint

                response = requests.post(url, json=data, timeout=timeout)
                response.raise_for_status()

                # Parse response based on endpoint
                result = response.json()

                if endpoint == "/v1/completions":
                    choices = result.get("choices", [])
                    if choices:
                        return choices[0].get("text", "No response generated").strip()
                    return "No response generated"
                elif endpoint == "/api/generate":
                    return result.get("response", "No response generated").strip()
                elif endpoint == "/api/chat":
                    return result.get("message", {}).get("content", "No response generated").strip()

            except requests.exceptions.Timeout as e:
                last_error = f"Timeout on {endpoint}: {str(e)}"
                print(f"Warning: {last_error}")
                continue  # Try next endpoint
            except requests.exceptions.HTTPError as e:
                if e.response.status_code in [404, 405]:
                    last_error = f"Endpoint {endpoint} not available (HTTP {e.response.status_code})"
                    print(f"Warning: {last_error}")
                    continue  # Try next endpoint
                else:
                    last_error = f"HTTP Error {e.response.status_code} on {endpoint}: {str(e)}"
                    print(f"Error: {last_error}")
                    continue  # Try next endpoint
            except requests.exceptions.ConnectionError as e:
                last_error = f"Connection error on {endpoint}: {str(e)}"
                print(f"Error: {last_error}")
                continue  # Try next endpoint
            except Exception as e:
                last_error = f"Unexpected error on {endpoint}: {str(e)}"
                print(f"Error: {last_error}")
                continue  # Try next endpoint

        # If all endpoints failed, return detailed error message
        return f"Error: All API endpoints failed. Last error: {last_error}. Make sure Ollama is running and the model '{self.model_name}' is available."

class PDFProcessor:
    def __init__(self):
        self.chunk_size = 1000
        self.chunk_overlap = 200
    
    def extract_text_and_ocr(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extract text from PDF using both direct text extraction and OCR"""
        doc = fitz.open(pdf_path)
        chunks = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            
            # Try direct text extraction first
            text = page.get_text()
            
            # If no text found, use OCR
            if not text.strip():
                pix = page.get_pixmap()
                img_data = pix.tobytes("png")
                img = Image.open(io.BytesIO(img_data))
                text = pytesseract.image_to_string(img)
            
            if text.strip():
                # Split into chunks
                page_chunks = self._split_text(text, page_num + 1)
                chunks.extend(page_chunks)
        
        doc.close()
        return chunks
    
    def _split_text(self, text: str, page_num: int) -> List[Dict[str, Any]]:
        """Split text into overlapping chunks"""
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk_text = ' '.join(chunk_words)
            
            chunks.append({
                'text': chunk_text,
                'page': page_num,
                'chunk_id': f"page_{page_num}_chunk_{len(chunks)}"
            })
        
        return chunks

class VectorStore:
    def __init__(self, persist_directory="./chroma_db"):
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.persist_directory = persist_directory
        
        # Initialize ChromaDB
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )
        
        try:
            self.collection = self.client.get_collection("knowledge_base")
        except:
            self.collection = self.client.create_collection("knowledge_base")
    
    def add_documents(self, chunks: List[Dict[str, Any]], filename: str):
        """Add document chunks to vector store"""
        texts = [chunk['text'] for chunk in chunks]
        embeddings = self.embedding_model.encode(texts).tolist()
        
        ids = [f"{filename}_{chunk['chunk_id']}" for chunk in chunks]
        metadatas = [
            {
                'filename': filename,
                'page': chunk['page'],
                'chunk_id': chunk['chunk_id']
            }
            for chunk in chunks
        ]
        
        self.collection.upsert(
            embeddings=embeddings,
            documents=texts,
            metadatas=metadatas,
            ids=ids
        )
    
    def similarity_search(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar documents"""
        query_embedding = self.embedding_model.encode([query]).tolist()
        
        results = self.collection.query(
            query_embeddings=query_embedding,
            n_results=k,
            include=['documents', 'metadatas', 'distances']
        )
        
        search_results = []
        for i in range(len(results['documents'][0])):
            search_results.append({
                'text': results['documents'][0][i],
                'metadata': results['metadatas'][0][i],
                'distance': results['distances'][0][i]
            })
        
        return search_results
    
    def get_collection_info(self):
        """Get information about the collection"""
        return self.collection.count()

class RAGSystem:
    def __init__(self):
        self.llm = OllamaLLM()
        self.vector_store = VectorStore()
        self.pdf_processor = PDFProcessor()
    
    def add_pdf(self, pdf_file, filename: str) -> str:
        """Process and add PDF to knowledge base"""
        try:
            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                tmp_file.write(pdf_file.read())
                tmp_path = tmp_file.name
            
            # Process PDF
            chunks = self.pdf_processor.extract_text_and_ocr(tmp_path)
            
            if not chunks:
                return "No text could be extracted from the PDF."
            
            # Add to vector store
            self.vector_store.add_documents(chunks, filename)
            
            # Clean up
            os.unlink(tmp_path)
            
            return f"Successfully processed {filename}. Added {len(chunks)} chunks to knowledge base."
        
        except Exception as e:
            return f"Error processing PDF: {str(e)}"
    
    def query(self, question: str, k: int = 5) -> Dict[str, Any]:
        """Query the knowledge base"""
        try:
            # Retrieve relevant documents
            search_results = self.vector_store.similarity_search(question, k=k)
            
            if not search_results:
                return {
                    'answer': "No relevant documents found in the knowledge base.",
                    'sources': []
                }
            
            # Prepare context
            context = "\n\n".join([
                f"Document: {result['metadata']['filename']} (Page {result['metadata']['page']})\n{result['text']}"
                for result in search_results
            ])
            
            # Generate prompt
            prompt = f"""Based on the following context documents, please answer the question. If the answer cannot be found in the context, say so.

Context:
{context}

Question: {question}

Answer:"""
            
            # Generate response
            answer = self.llm.generate(prompt, max_tokens=512)
            
            # Prepare sources
            sources = [
                {
                    'filename': result['metadata']['filename'],
                    'page': result['metadata']['page'],
                    'relevance_score': 1 - result['distance']
                }
                for result in search_results
            ]
            
            return {
                'answer': answer,
                'sources': sources,
                'context_used': len(search_results)
            }
        
        except Exception as e:
            return {
                'answer': f"Error processing query: {str(e)}",
                'sources': []
            }

# Streamlit UI
def main():
    try:
        st.set_page_config(
            page_title="Local Knowledge Base",
            page_icon="📚",
            layout="wide"
        )
        
        st.title("🧠 Local Knowledge Base with RAG")
        st.markdown("Upload PDFs and query your local knowledge base using Llama 3")
        
        # Check dependencies
        missing_deps = []
        if not HAS_PYMUPDF:
            missing_deps.append("PyMuPDF")
        if not HAS_OCR:
            missing_deps.append("pytesseract, pillow")
        if not HAS_EMBEDDINGS:
            missing_deps.append("sentence-transformers")
        if not HAS_CHROMADB:
            missing_deps.append("chromadb")
        
        if missing_deps:
            st.error(f"Missing dependencies: {', '.join(missing_deps)}")
            st.code(f"pip install {' '.join(missing_deps)}")
            st.stop()
        
        # Test Ollama connection
        ollama_status = test_ollama_connection()
        if not ollama_status['connected']:
            st.error("Ollama is not running or not accessible")
            st.markdown("""
            **To fix this:**
            1. Start Ollama service: `ollama serve`
            2. Pull Llama 3 model: `ollama pull llama3:8b`
            3. Refresh this page
            """)
            st.info(f"Error: {ollama_status['error']}")
            st.stop()
        else:
            st.success(f"✅ Connected to Ollama - Model: {ollama_status['model']}")
        
        # Initialize RAG system
        if 'rag_system' not in st.session_state:
            with st.spinner("Initializing system..."):
                try:
                    st.session_state.rag_system = RAGSystem()
                    st.success("✅ System initialized successfully")
                except Exception as e:
                    st.error(f"Failed to initialize system: {str(e)}")
                    st.code(traceback.format_exc())
                    st.stop()
        
        # Sidebar for file upload
        with st.sidebar:
            st.header("📁 Document Management")
            
            uploaded_file = st.file_uploader(
                "Upload PDF",
                type=['pdf'],
                help="Upload a PDF document to add to your knowledge base"
            )
            
            if uploaded_file is not None:
                if st.button("Process PDF"):
                    with st.spinner(f"Processing {uploaded_file.name}..."):
                        try:
                            result = st.session_state.rag_system.add_pdf(
                                uploaded_file, 
                                uploaded_file.name
                            )
                            st.success(result)
                        except Exception as e:
                            st.error(f"Error processing PDF: {str(e)}")
            
            # Show collection info
            st.subheader("📊 Knowledge Base Stats")
            try:
                doc_count = st.session_state.rag_system.vector_store.get_collection_info()
                st.metric("Documents in KB", doc_count)
            except Exception as e:
                st.metric("Documents in KB", "Error")
                st.caption(f"Error: {str(e)}")
        
        # Main query interface
        st.header("🔍 Query Your Knowledge Base")
        
        query = st.text_input(
            "Ask a question:",
            placeholder="What would you like to know from your documents?"
        )
        
        col1, col2 = st.columns([3, 1])
        
        with col2:
            num_results = st.number_input(
                "Results to retrieve:",
                min_value=1,
                max_value=10,
                value=5
            )

        with col1:
            if st.button("Search", type="primary"):
                if query:
                    with st.spinner("Searching knowledge base..."):
                        try:
                            result = st.session_state.rag_system.query(query, k=num_results)

                            # Display answer
                            st.subheader("📝 Answer")
                            st.write(result['answer'])

                            # Display sources
                            if result.get('sources'):
                                st.subheader("📚 Sources")
                                for i, source in enumerate(result['sources'], 1):
                                    st.write(f"{i}. **{source['filename']}** (Page {source['page']}) - Relevance: {source.get('relevance_score', 0):.2f}")
                        except Exception as e:
                            st.error(f"Error during search: {str(e)}")
                            st.code(traceback.format_exc())
                else:
                    st.warning("Please enter a question.")
        
        # System status
        with st.expander("🔧 System Status"):
            st.write("**Dependencies:**")
            st.write(f"- PyMuPDF: {'✅' if HAS_PYMUPDF else '❌'}")
            st.write(f"- OCR (Tesseract): {'✅' if HAS_OCR else '❌'}")
            st.write(f"- Embeddings: {'✅' if HAS_EMBEDDINGS else '❌'}")
            st.write(f"- ChromaDB: {'✅' if HAS_CHROMADB else '❌'}")
            st.write(f"- Ollama: {'✅' if ollama_status['connected'] else '❌'}")
        
        # Instructions
        with st.expander("ℹ️ How to use"):
            st.markdown("""
            1. **Upload PDFs**: Use the sidebar to upload PDF documents
            2. **Process Documents**: Click 'Process PDF' to add them to your knowledge base
            3. **Ask Questions**: Enter your questions in the search box
            4. **Review Results**: Get AI-generated answers with source citations
            
            **Features:**
            - OCR support for scanned PDFs
            - Vector similarity search
            - Local Llama 3 processing
            - Source attribution
            - Persistent storage
            """)
            
    except Exception as e:
        st.error(f"Application error: {str(e)}")
        st.code(traceback.format_exc())

def test_ollama_connection():
    """Test if Ollama is running and has the required model"""
    try:
        # Test multiple endpoints
        test_endpoints = [
            ("http://127.0.0.1:11434/api/tags", "GET"),
            ("http://127.0.0.1:11434/api/ps", "GET"),
            ("http://127.0.0.1:11434/", "GET")
        ]
        
        ollama_running = False
        available_models = []
        
        for url, method in test_endpoints:
            try:
                if method == "GET":
                    response = requests.get(url, timeout=5)
                else:
                    response = requests.post(url, timeout=5)
                
                if response.status_code == 200:
                    ollama_running = True
                    if "tags" in url:
                        data = response.json()
                        available_models = [m.get('name', 'unknown') for m in data.get('models', [])]
                    break
            except:
                continue
        
        if not ollama_running:
            return {
                'connected': False,
                'model': None,
                'error': 'Cannot connect to Ollama. Make sure Ollama is running: ollama serve'
            }
        
        # Check for Llama models
        llama_models = [m for m in available_models if 'llama' in m.lower()]
        
        if llama_models:
            return {
                'connected': True,
                'model': llama_models[0],
                'error': None,
                'all_models': available_models
            }
        else:
            return {
                'connected': True,
                'model': None,
                'error': 'Llama 3 model not found. Run: ollama pull llama3:8b',
                'all_models': available_models
            }
            
    except Exception as e:
        return {
            'connected': False,
            'model': None,
            'error': str(e)
        }

if __name__ == "__main__":
    main()