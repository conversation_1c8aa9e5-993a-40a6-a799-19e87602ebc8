import streamlit as st
import sys
import traceback

def main():
    try:
        st.title("🧪 Knowledge Base System Test")
        st.write("Testing basic functionality...")
        
        # Test 1: Basic Streamlit
        st.success("✅ Streamlit is working")
        
        # Test 2: Import testing
        st.subheader("🔍 Testing Dependencies")
        
        deps_status = {}
        
        # Test PyMuPDF
        try:
            import fitz
            deps_status['PyMuPDF'] = "✅ Installed"
        except ImportError as e:
            deps_status['PyMuPDF'] = f"❌ Not installed: {e}"
        
        # Test OCR
        try:
            import pytesseract
            from PIL import Image
            deps_status['OCR (pytesseract + PIL)'] = "✅ Installed"
        except ImportError as e:
            deps_status['OCR (pytesseract + PIL)'] = f"❌ Not installed: {e}"
        
        # Test sentence-transformers
        try:
            from sentence_transformers import SentenceTransformer
            deps_status['sentence-transformers'] = "✅ Installed"
        except ImportError as e:
            deps_status['sentence-transformers'] = f"❌ Not installed: {e}"
        
        # Test ChromaDB
        try:
            import chromadb
            deps_status['ChromaDB'] = "✅ Installed"
        except ImportError as e:
            deps_status['ChromaDB'] = f"❌ Not installed: {e}"
        
        # Test requests
        try:
            import requests
            deps_status['requests'] = "✅ Installed"
        except ImportError as e:
            deps_status['requests'] = f"❌ Not installed: {e}"
        
        # Display results
        for dep, status in deps_status.items():
            st.write(f"**{dep}**: {status}")
        
        # Test 3: Ollama connection
        st.subheader("🦙 Testing Ollama Connection")
        
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                st.success(f"✅ Ollama is running with {len(models)} models")
                
                llama_models = [m for m in models if 'llama' in m.get('name', '').lower()]
                if llama_models:
                    st.success(f"✅ Found Llama models: {[m['name'] for m in llama_models]}")
                else:
                    st.warning("⚠️ No Llama models found. Run: ollama pull llama3:8b")
                
                # Show all models
                if models:
                    st.write("Available models:")
                    for model in models:
                        st.write(f"- {model['name']}")
            else:
                st.error(f"❌ Ollama API returned status {response.status_code}")
        
        except requests.exceptions.ConnectionError:
            st.error("❌ Cannot connect to Ollama. Make sure it's running: `ollama serve`")
        except Exception as e:
            st.error(f"❌ Error testing Ollama: {e}")
        
        # Test 4: Directory creation
        st.subheader("📁 Testing File System")
        
        try:
            import os
            import tempfile
            
            # Test temp directory
            with tempfile.TemporaryDirectory() as temp_dir:
                st.success(f"✅ Can create temporary directories: {temp_dir}")
            
            # Test ChromaDB directory
            chroma_dir = "./chroma_db"
            if not os.path.exists(chroma_dir):
                os.makedirs(chroma_dir)
                st.success(f"✅ Created ChromaDB directory: {chroma_dir}")
            else:
                st.success(f"✅ ChromaDB directory exists: {chroma_dir}")
                
        except Exception as e:
            st.error(f"❌ File system error: {e}")
        
        # Test 5: Basic embedding test
        st.subheader("🧮 Testing Embeddings")
        
        try:
            from sentence_transformers import SentenceTransformer
            
            with st.spinner("Loading embedding model..."):
                model = SentenceTransformer('all-MiniLM-L6-v2')
                test_text = ["This is a test sentence."]
                embeddings = model.encode(test_text)
                st.success(f"✅ Embeddings working. Shape: {embeddings.shape}")
                
        except Exception as e:
            st.error(f"❌ Embedding test failed: {e}")
            st.code(traceback.format_exc())
        
        # Installation commands
        st.subheader("🛠️ Installation Commands")
        
        missing_packages = []
        for dep, status in deps_status.items():
            if "❌" in status:
                if "PyMuPDF" in dep:
                    missing_packages.append("PyMuPDF")
                elif "OCR" in dep:
                    missing_packages.extend(["pytesseract", "pillow"])
                elif "sentence-transformers" in dep:
                    missing_packages.append("sentence-transformers")
                elif "ChromaDB" in dep:
                    missing_packages.append("chromadb")
                elif "requests" in dep:
                    missing_packages.append("requests")
        
        if missing_packages:
            st.code(f"pip install {' '.join(set(missing_packages))}")
        else:
            st.success("✅ All Python dependencies are installed!")
        
        # System info
        st.subheader("💻 System Info")
        st.write(f"Python version: {sys.version}")
        st.write(f"Streamlit version: {st.__version__}")
        
    except Exception as e:
        st.error(f"Test failed with error: {e}")
        st.code(traceback.format_exc())

if __name__ == "__main__":
    main()