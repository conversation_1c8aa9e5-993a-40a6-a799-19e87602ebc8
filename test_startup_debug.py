#!/usr/bin/env python3
"""
Debug startup script to identify issues with the knowledge base system
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print("✅ Python version OK")
    return True

def check_package(package_name, import_name=None):
    """Check if a package is installed and importable"""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is None:
            print(f"❌ {package_name} not found")
            return False
        
        # Try to import
        importlib.import_module(import_name)
        print(f"✅ {package_name} OK")
        return True
    except Exception as e:
        print(f"❌ {package_name} import failed: {e}")
        return False

def check_ollama():
    """Check if Ollama is running"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama running with {len(models)} models")
            
            llama_models = [m for m in models if 'llama' in m.get('name', '').lower()]
            if llama_models:
                print(f"✅ Llama models: {[m['name'] for m in llama_models]}")
                return True
            else:
                print("⚠️  No Llama models found")
                print("Run: ollama pull llama3:8b")
                return False
        else:
            print(f"❌ Ollama API error: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        print("Make sure Ollama is running: ollama serve")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Tesseract OCR installed")
            return True
        else:
            print("❌ Tesseract OCR not working")
            return False
    except FileNotFoundError:
        print("❌ Tesseract OCR not found")
        print("Install with: sudo apt install tesseract-ocr (Linux) or brew install tesseract (Mac)")
        return False
    except Exception as e:
        print(f"❌ Tesseract check failed: {e}")
        return False

def check_directories():
    """Check if required directories can be created"""
    try:
        os.makedirs("chroma_db", exist_ok=True)
        os.makedirs("uploads", exist_ok=True)
        print("✅ Directories OK")
        return True
    except Exception as e:
        print(f"❌ Directory creation failed: {e}")
        return False

def main():
    print("🔍 Knowledge Base System Diagnostic")
    print("=" * 40)
    
    all_ok = True
    
    # Check Python version
    if not check_python_version():
        all_ok = False
    
    print("\n📦 Checking Python packages...")
    packages = [
        ("streamlit", "streamlit"),
        ("PyMuPDF", "fitz"),
        ("pytesseract", "pytesseract"),
        ("Pillow", "PIL"),
        ("sentence-transformers", "sentence_transformers"),
        ("chromadb", "chromadb"),
        ("requests", "requests"),
    ]
    
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            all_ok = False
    
    print("\n🦙 Checking Ollama...")
    if not check_ollama():
        all_ok = False
    
    print("\n👁️ Checking Tesseract OCR...")
    if not check_tesseract():
        all_ok = False
    
    print("\n📁 Checking directories...")
    if not check_directories():
        all_ok = False
    
    print("\n" + "=" * 40)
    if all_ok:
        print("✅ All checks passed! You can run the application:")
        print("streamlit run local_knowledge_base.py")
    else:
        print("❌ Some issues need to be fixed first.")
        print("\nTo install missing Python packages:")
        print("pip install streamlit PyMuPDF pytesseract pillow sentence-transformers chromadb requests")
        print("\nTo start Ollama:")
        print("ollama serve")
        print("ollama pull llama3:8b")

if __name__ == "__main__":
    main()